{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cce6766b-9aee-4c17-8df7-5b39b45b9db0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Aggregated Customer Purchase Activity - Gold Layer Materialized View\n", "\n", "This notebook creates an aggregated materialized view in the gold layer that identifies customers who have made purchases in the last quarter. It uses data from the silver.customer_cleaned and silver.invoice_line tables."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa06b87d-8138-445a-b0d4-39d2819f695d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import (\n", "    col, current_timestamp, lit, datediff, date_sub, max, when, count, sum as sql_sum, \n", "    avg, min, max, expr, quarter, year, month, dayofmonth, to_date, concat\n", ")\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "\n", "# Use the logger configuration from startup with log_path from config\n", "customer_activity_logger = create_logger(component_log_levels=component_log_levels)\n", "customer_activity_logger.info(\"Initializing notebook\")\n", " \n", "# Extract frequently used config values into variables\n", "catalog = pipeline_config[\"catalog\"]\n", "silver_schema = pipeline_config[\"schemas\"][\"silver\"]\n", "gold_schema = pipeline_config[\"schemas\"][\"gold\"]\n", "gold_path = pipeline_config[\"paths\"][\"gold_path\"]\n", "gold_format = pipeline_config[\"file_formats\"][\"gold\"]\n", "materialized_view_properties = pipeline_config[\"materialized_view_properties\"]\n", "\n", "# Switch to catalog\n", "spark.sql(f\"USE CATALOG {catalog}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d3fbae4c-e504-40ad-bd0e-54035b2beaf1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to read data from silver layer\n", "@log_execution(customer_activity_logger)\n", "def read_silver_data():\n", "    customer_activity_logger.info(\"Reading customer and invoice_line data from silver layer\")\n", "    \n", "    # Read customer data\n", "    try:\n", "        customer_df = spark.table(f\"{catalog}.{silver_schema}.customer_cleaned\")\n", "        customer_activity_logger.info(f\"Successfully read customer data with {customer_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        customer_activity_logger.error(f\"Error reading customer data: {str(e)}\")\n", "        return None, None\n", "    \n", "    # Read invoice_line data\n", "    try:\n", "        invoice_line_df = spark.table(f\"{catalog}.{silver_schema}.invoice_line_cleaned\")\n", "        customer_activity_logger.info(f\"Successfully read invoice_line data with {invoice_line_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        customer_activity_logger.error(f\"Error reading invoice_line data: {str(e)}\")\n", "        return customer_df, None\n", "    \n", "    return customer_df, invoice_line_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "234247ab-033a-4431-81ad-209eb7e59a91", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to create customer purchase activity view\n", "@log_execution(customer_activity_logger)\n", "def create_customer_purchase_activity(customer_df, invoice_line_df):\n", "    customer_activity_logger.info(\"Creating customer purchase activity view\")\n", "    \n", "    if customer_df is None or invoice_line_df is None:\n", "        customer_activity_logger.error(\"Missing required data for customer purchase activity view\")\n", "        return None\n", "    \n", "    # Calculate current date and the start of the previous calendar quarter\n", "    current_date = expr(\"current_date()\")\n", "    \n", "    # Get the current year and quarter\n", "    current_year = year(current_date)\n", "    current_quarter = quarter(current_date)\n", "    \n", "    # Calculate the previous quarter and its year\n", "    prev_quarter = when(current_quarter > 1, current_quarter - 1).otherwise(4)\n", "    prev_quarter_year = when(current_quarter > 1, current_year).otherwise(current_year - 1)\n", "    \n", "    # Calculate the start date of the previous quarter\n", "    last_quarter_start = (\n", "        when(prev_quarter == 1, to_date(concat(prev_quarter_year, lit(\"-01-01\"))))\n", "        .when(prev_quarter == 2, to_date(concat(prev_quarter_year, lit(\"-04-01\"))))\n", "        .when(prev_quarter == 3, to_date(concat(prev_quarter_year, lit(\"-07-01\"))))\n", "        .when(prev_quarter == 4, to_date(concat(prev_quarter_year, lit(\"-10-01\"))))\n", "    )\n", "    \n", "    # Get the most recent purchase date for each customer\n", "    customer_purchases = invoice_line_df.groupBy(\"customer_id\").agg(\n", "        max(\"invoice_date\").alias(\"last_purchase_date\"),\n", "        count(\"*\").alias(\"total_purchases\"),\n", "        sql_sum(\"line_total_amount\").alias(\"total_spend\"),\n", "        expr(\"count(distinct invoice_id)\").alias(\"total_invoices\")\n", "    )\n", "    \n", "    # Determine if the customer has purchased in the last quarter\n", "    customer_purchases = customer_purchases.withColumn(\n", "        \"purchased_last_quarter\",\n", "        when(col(\"last_purchase_date\") >= last_quarter_start, True).otherwise(False)\n", "    )\n", "    \n", "    # Add quarter information for analysis\n", "    customer_purchases = customer_purchases.withColumn(\n", "        \"last_purchase_quarter\", \n", "        quarter(col(\"last_purchase_date\"))\n", "    )\n", "    customer_purchases = customer_purchases.withColumn(\n", "        \"last_purchase_year\", \n", "        year(col(\"last_purchase_date\"))\n", "    )\n", "    \n", "    # Add quarter start and end dates for the previous quarter\n", "    customer_purchases = customer_purchases.withColumn(\n", "        \"quarter_start_date\", \n", "        last_quarter_start\n", "    )\n", "    \n", "    # Calculate the end date of the previous quarter\n", "    quarter_end_date = (\n", "        when(prev_quarter == 1, to_date(concat(prev_quarter_year, lit(\"-03-31\"))))\n", "        .when(prev_quarter == 2, to_date(concat(prev_quarter_year, lit(\"-06-30\"))))\n", "        .when(prev_quarter == 3, to_date(concat(prev_quarter_year, lit(\"-09-30\"))))\n", "        .when(prev_quarter == 4, to_date(concat(prev_quarter_year, lit(\"-12-31\"))))\n", "    )\n", "    \n", "    customer_purchases = customer_purchases.withColumn(\n", "        \"quarter_end_date\", \n", "        quarter_end_date\n", "    )\n", "    \n", "    # Calculate days since last purchase\n", "    customer_purchases = customer_purchases.withColumn(\n", "        \"days_since_last_purchase\",\n", "        datediff(current_date, col(\"last_purchase_date\"))\n", "    )\n", "    \n", "    # Join with customer data to get customer details\n", "    result_df = customer_df.join(\n", "        customer_purchases,\n", "        customer_df.customer_id == customer_purchases.customer_id,\n", "        \"left\"\n", "    ).drop(customer_purchases.customer_id)\n", "    \n", "    # Handle customers with no purchase history\n", "    result_df = result_df.withColumn(\n", "        \"purchased_last_quarter\",\n", "        when(col(\"last_purchase_date\").isNull(), False).otherwise(col(\"purchased_last_quarter\"))\n", "    )\n", "    \n", "    # Add customer status based on purchase activity\n", "    result_df = result_df.withColumn(\n", "        \"customer_status\",\n", "        when(col(\"last_purchase_date\").isNull(), \"Never Purchased\")\n", "        .when(col(\"purchased_last_quarter\"), \"Active\")\n", "        .when(col(\"days_since_last_purchase\") <= 180, \"Recent\")\n", "        .when(col(\"days_since_last_purchase\") <= 365, \"Lapsed\")\n", "        .otherwise(\"Inactive\")\n", "    )\n", "    \n", "    # Add metadata\n", "    result_df = result_df.withColumn(\"dw_created_at\", current_timestamp())\n", "    \n", "    return log_dataframe_info(result_df, \"customer_purchase_activity\", customer_activity_logger)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to create materialized view\n", "@log_execution(customer_activity_logger)\n", "def create_materialized_view(df, view_name):\n", "    customer_activity_logger.info(f\"Creating materialized view: {view_name}\")\n", "    \n", "    if df is None:\n", "        customer_activity_logger.error(\"No data available to create materialized view\")\n", "        return False\n", "    \n", "    target_path = f\"{gold_path}/{view_name}\"\n", "    \n", "    try:\n", "        # Select only the required columns for the materialized view\n", "        required_columns = [\n", "            \"customer_id\",\n", "            \"customer_type\",\n", "            \"customer_email_address\",\n", "            \"registration_date\",\n", "            \"last_purchase_date\",\n", "            \"quarter_start_date\",\n", "            \"quarter_end_date\",\n", "            \"purchased_last_quarter\",\n", "            \"days_since_last_purchase\",\n", "            \"dw_created_at\"  # Keep metadata column\n", "        ]\n", "        \n", "        # Filter the DataFrame to only include the required columns\n", "        filtered_df = df.select(required_columns)\n", "        \n", "        # Write data to Delta table\n", "        filtered_df.write.format(gold_format).mode(\"overwrite\").option(\"path\", target_path).saveAsTable(f\"{catalog}.{gold_schema}.{view_name}\")\n", "        \n", "        # Set table properties for optimization\n", "        spark.sql(f\"\"\"\n", "        ALTER TABLE {catalog}.{gold_schema}.{view_name} SET TBLPROPERTIES (\n", "            {materialized_view_properties}\n", "        )\n", "        \"\"\")\n", "        \n", "        customer_activity_logger.info(f\"Successfully created aggregated materialized view: {view_name}\")\n", "        return True\n", "    except Exception as e:\n", "        customer_activity_logger.error(f\"Error creating materialized view: {str(e)}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Main execution function\n", "@log_execution(customer_activity_logger)\n", "def process_customer_purchase_activity():\n", "    customer_activity_logger.info(\"Processing customer purchase activity\")\n", "    \n", "    # Read data from silver layer\n", "    customer_df, invoice_line_df = read_silver_data()\n", "    \n", "    # Create customer purchase activity view\n", "    activity_df = create_customer_purchase_activity(customer_df, invoice_line_df)\n", "    \n", "    # Create materialized view\n", "    if activity_df is not None:\n", "        create_materialized_view(activity_df, \"agg_customer_purchase_activity\")\n", "        return activity_df\n", "    else:\n", "        customer_activity_logger.error(\"Failed to create customer purchase activity view\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Execute the process\n", "activity_df = process_customer_purchase_activity()\n", "if activity_df is not None:\n", "    # Select only the requested columns for display\n", "    display_columns = [\n", "        \"customer_id\",\n", "        \"customer_type\",\n", "        \"customer_email_address\",\n", "        \"registration_date\",\n", "        \"last_purchase_date\",\n", "        \"quarter_start_date\",\n", "        \"quarter_end_date\",\n", "        \"purchased_last_quarter\",\n", "        \"days_since_last_purchase\"\n", "    ]\n", "    \n", "    # Create a filtered view with only the requested columns\n", "    filtered_df = activity_df.select(display_columns)\n", "    \n", "    # Display the filtered DataFrame\n", "    display(filtered_df)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": null, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5135065695671098, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "customer_purchase_activity", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}