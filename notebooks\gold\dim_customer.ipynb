{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "cce6766b-9aee-4c17-8df7-5b39b45b9db0", "showTitle": false, "title": ""}}, "source": ["# Customer Dimension Table\n", "\n", "This notebook creates and maintains the customer dimension table in the gold layer. It enriches customer data with additional attributes and metrics based on transaction history."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa06b87d-8138-445a-b0d4-39d2819f695d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import (\n", "    current_timestamp, lit, col, datediff, count, sum as sql_sum, \n", "    avg, min, max, when, concat_ws, upper, expr\n", ")\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "from pyspark.sql.window import Window\n", "\n", "# Use the logger configuration from startup\n", "dim_customer_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)\n", "dim_customer_logger.info(\"Initializing notebook\")\n", " \n", "# Extract frequently used config values into variables\n", "catalog = pipeline_config[\"catalog\"]\n", "silver_schema = pipeline_config[\"schemas\"][\"silver\"]\n", "gold_schema = pipeline_config[\"schemas\"][\"gold\"]\n", "silver_path = pipeline_config[\"paths\"][\"silver_path\"]\n", "gold_path = pipeline_config[\"paths\"][\"gold_path\"]\n", "gold_format = pipeline_config[\"file_formats\"][\"gold\"]\n", "delta_properties = pipeline_config[\"delta_properties\"]\n", "\n", "# Switch to catalog\n", "spark.sql(f\"USE CATALOG {catalog}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "234247ab-033a-4431-81ad-209eb7e59a91", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to read data from silver layer\n", "@log_execution(dim_customer_logger)\n", "def read_silver_data():\n", "    dim_customer_logger.info(\"Reading customer data from silver layer\")\n", "    \n", "    # Read customer data\n", "    try:\n", "        customer_df = spark.table(f\"{catalog}.{silver_schema}.customer\")\n", "        dim_customer_logger.info(f\"Successfully read customer data with {customer_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        dim_customer_logger.error(f\"Error reading customer data: {str(e)}\")\n", "        return None, None, None\n", "    \n", "    # Read invoice data if available\n", "    try:\n", "        invoice_df = spark.table(f\"{catalog}.{silver_schema}.invoice\")\n", "        dim_customer_logger.info(f\"Successfully read invoice data with {invoice_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        dim_customer_logger.warning(f\"Invoice data not available: {str(e)}\")\n", "        invoice_df = None\n", "    \n", "    # Read invoice_line data if available\n", "    try:\n", "        invoice_line_df = spark.table(f\"{catalog}.{silver_schema}.invoice_line\")\n", "        dim_customer_logger.info(f\"Successfully read invoice_line data with {invoice_line_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        dim_customer_logger.warning(f\"Invoice line data not available: {str(e)}\")\n", "        invoice_line_df = None\n", "    \n", "    return customer_df, invoice_df, invoice_line_df\n", "\n", "# Function to calculate customer metrics\n", "@log_execution(dim_customer_logger)\n", "def calculate_customer_metrics(customer_df, invoice_df, invoice_line_df):\n", "    dim_customer_logger.info(\"Calculating customer metrics\")\n", "    \n", "    if invoice_df is None or invoice_line_df is None:\n", "        dim_customer_logger.warning(\"Invoice or invoice_line data is missing. Skipping customer metrics calculation.\")\n", "        return customer_df\n", "    \n", "    # Join invoice and invoice_line tables\n", "    sales_df = invoice_line_df.join(\n", "        invoice_df,\n", "        invoice_line_df.invoice_id == invoice_df.invoice_id,\n", "        \"inner\"\n", "    )\n", "    \n", "    # Calculate customer metrics\n", "    customer_metrics = sales_df.groupBy(\"customer_id\").agg(\n", "        sql_sum(\"line_total_amount\").alias(\"lifetime_value\"),\n", "        count(\"distinct invoice_id\").alias(\"total_orders\"),\n", "        avg(\"line_total_amount\").alias(\"average_order_value\"),\n", "        min(\"invoiceDate\").alias(\"first_purchase_date\"),\n", "        max(\"invoiceDate\").alias(\"last_purchase_date\"),\n", "        count(\"distinct item_id\").alias(\"unique_products_purchased\")\n", "    )\n", "    \n", "    # Calculate days since last purchase\n", "    customer_metrics = customer_metrics.withColumn(\n", "        \"days_since_last_purchase\",\n", "        datediff(current_timestamp(), col(\"last_purchase_date\"))\n", "    )\n", "    \n", "    # Join metrics with customer data\n", "    customer_df = customer_df.join(\n", "        customer_metrics,\n", "        customer_df.customer_id == customer_metrics.customer_id,\n", "        \"left\"\n", "    ).drop(customer_metrics.customer_id)\n", "    \n", "    # Add customer segments based on RFM (Recency, Frequency, Monetary)\n", "    customer_df = customer_df.withColumn(\n", "        \"customer_segment\",\n", "        when(col(\"lifetime_value\").isNull(), \"New\")\n", "        .when(col(\"days_since_last_purchase\") <= 30 & col(\"lifetime_value\") >= 1000, \"VIP\")\n", "        .when(col(\"days_since_last_purchase\") <= 90 & col(\"total_orders\") >= 3, \"Loyal\")\n", "        .when(col(\"days_since_last_purchase\") <= 180, \"Active\")\n", "        .when(col(\"days_since_last_purchase\") > 180 & col(\"days_since_last_purchase\") <= 365, \"At Risk\")\n", "        .when(col(\"days_since_last_purchase\") > 365, \"Inactive\")\n", "        .otherwise(\"Unknown\")\n", "    )\n", "    \n", "    return customer_df\n", "\n", "# Function to create dimension table\n", "@log_execution(dim_customer_logger)\n", "def create_dimension_table(customer_df, invoice_df, invoice_line_df):\n", "    dim_customer_logger.info(\"Creating customer dimension table\")\n", "    \n", "    # Calculate customer metrics if transaction data is available\n", "    if invoice_df is not None and invoice_line_df is not None:\n", "        customer_df = calculate_customer_metrics(customer_df, invoice_df, invoice_line_df)\n", "    \n", "    # Select relevant columns for dimension table\n", "    columns_to_select = [\n", "        \"customer_id\", \"customer_name\", \"customer_type\", \"email\", \"phone_number\", \n", "        \"address\", \"city\", \"province\", \"postal_code\", \"registration_date\", \n", "        \"is_active\", \"loyalty_card_number\", \"loyalty_tier\", \"created_timestamp\", \n", "        \"modified_timestamp\", \"customer_tenure_days\", \"data_quality_score\"\n", "    ]\n", "    \n", "    # Add transaction metrics if available\n", "    transaction_columns = [\n", "        \"lifetime_value\", \"total_orders\", \"average_order_value\", \"first_purchase_date\",\n", "        \"last_purchase_date\", \"days_since_last_purchase\", \"unique_products_purchased\",\n", "        \"customer_segment\"\n", "    ]\n", "    \n", "    # Check which columns exist in the dataframe\n", "    available_columns = [col for col in columns_to_select + transaction_columns if col in customer_df.columns]\n", "    \n", "    # Create dimension table with available columns\n", "    dim_df = customer_df.select(*available_columns)\n", "    \n", "    # Add data warehouse timestamp\n", "    dim_df = dim_df.withColumn(\"dw_created_at\", current_timestamp())\n", "    \n", "    return log_dataframe_info(dim_df, \"dim_customer\", dim_customer_logger)\n", "\n", "# Function to write dimension table to gold layer\n", "@log_execution(dim_customer_logger)\n", "def write_dimension_table(df):\n", "    dim_customer_logger.info(\"Writing customer dimension table to gold layer\")\n", "    table_name = \"dim_customer\"\n", "    target_path = f\"{gold_path}/{table_name}\"\n", "    \n", "    # Create or replace the table in Unity Catalog\n", "    try:\n", "        # Check if table exists\n", "        spark.sql(f\"DESCRIBE TABLE {catalog}.{gold_schema}.{table_name}\")\n", "        table_exists = True\n", "    except AnalysisException:\n", "        table_exists = False\n", "    \n", "    # Write data using Delta format\n", "    if table_exists:\n", "        # Merge data using primary key\n", "        dim_customer_logger.info(f\"Merging data into existing table {catalog}.{gold_schema}.{table_name}\")\n", "        delta_table = DeltaTable.forName(spark, f\"{catalog}.{gold_schema}.{table_name}\")\n", "        \n", "        # Merge condition based on primary key\n", "        merge_condition = \"target.customer_id = source.customer_id\"\n", "        \n", "        # Perform merge operation\n", "        delta_table.alias(\"target\").merge(\n", "            df.alias(\"source\"),\n", "            merge_condition\n", "        ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()\n", "    else:\n", "        # Create new table\n", "        dim_customer_logger.info(f\"Creating new table {catalog}.{gold_schema}.{table_name}\")\n", "        df.write \\\n", "            .format(gold_format) \\\n", "            .option(\"mergeSchema\", \"true\") \\\n", "            .mode(\"overwrite\") \\\n", "            .saveAsTable(f\"{catalog}.{gold_schema}.{table_name}\")\n", "    \n", "    return df\n", "\n", "# Main processing function\n", "@log_execution(dim_customer_logger)\n", "def process_dimension_table():\n", "    dim_customer_logger.info(\"Processing customer dimension table\")\n", "    \n", "    # Read silver data\n", "    customer_df, invoice_df, invoice_line_df = read_silver_data()\n", "    if customer_df is None:\n", "        dim_customer_logger.error(\"Failed to read customer data\")\n", "        return None\n", "    \n", "    # Create dimension table\n", "    dim_df = create_dimension_table(customer_df, invoice_df, invoice_line_df)\n", "    \n", "    # Write to gold layer\n", "    final_df = write_dimension_table(dim_df)\n", "    \n", "    dim_customer_logger.info(\"Completed processing customer dimension table\")\n", "    return final_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Process customer dimension table\n", "dim_df = process_dimension_table()\n", "if dim_df is not None:\n", "    log_dataframe_info(dim_df, \"dim_customer_final\", dim_customer_logger)\n", "    display(dim_df)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "dim_customer", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}