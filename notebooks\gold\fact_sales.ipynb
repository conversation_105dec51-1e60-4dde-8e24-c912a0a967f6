{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "cce6766b-9aee-4c17-8df7-5b39b45b9db0", "showTitle": false, "title": ""}}, "source": ["# Fact Sales - Silver to Gold Transformation\n", "\n", "This notebook creates a fact_sales table in the gold layer by joining and aggregating data from the invoice and invoice_line tables in the silver layer. It provides a comprehensive view of sales data for analytics and reporting."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ef0eaaf-7985-4700-99b7-ba4a535ea410", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ../../start_up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa06b87d-8138-445a-b0d4-39d2819f695d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import (\n", "    current_timestamp, lit, col, to_date, date_format, concat, lpad,\n", "    year, month, dayofmonth, quarter, sum as sql_sum, count, avg, min, max,\n", "    when, coalesce, expr\n", ")\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "\n", "# Use the logger configuration from startup\n", "fact_sales_logger = create_logger(log_file=log_file, component_log_levels=component_log_levels)\n", "fact_sales_logger.info(\"Initializing notebook\")\n", " \n", "# Extract frequently used config values into variables\n", "catalog = pipeline_config[\"catalog\"]\n", "target_date = pipeline_config[\"default_processing_date\"]\n", "silver_schema = pipeline_config[\"schemas\"][\"silver\"]\n", "gold_schema = pipeline_config[\"schemas\"][\"gold\"]\n", "silver_path = pipeline_config[\"paths\"][\"silver_path\"]\n", "gold_path = pipeline_config[\"paths\"][\"gold_path\"]\n", "gold_format = pipeline_config[\"file_formats\"][\"gold\"]\n", "delta_properties = pipeline_config[\"delta_properties\"]\n", "\n", "# Switch to catalog\n", "spark.sql(f\"USE CATALOG {catalog}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "234247ab-033a-4431-81ad-209eb7e59a91", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Function to read data from silver layer\n", "@log_execution(fact_sales_logger)\n", "def read_silver_data():\n", "    fact_sales_logger.info(\"Reading silver data for invoice and invoice_line tables\")\n", "    \n", "    # Read invoice data\n", "    try:\n", "        invoice_df = spark.table(f\"{catalog}.{silver_schema}.invoice\")\n", "        fact_sales_logger.info(f\"Successfully read invoice data with {invoice_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        fact_sales_logger.error(f\"Error reading invoice data: {str(e)}\")\n", "        return None, None\n", "    \n", "    # Read invoice_line data\n", "    try:\n", "        invoice_line_df = spark.table(f\"{catalog}.{silver_schema}.invoice_line\")\n", "        fact_sales_logger.info(f\"Successfully read invoice_line data with {invoice_line_df.count()} rows\")\n", "    except AnalysisException as e:\n", "        fact_sales_logger.error(f\"Error reading invoice_line data: {str(e)}\")\n", "        return None, None\n", "    \n", "    return invoice_df, invoice_line_df\n", "\n", "# Function to create date dimension key\n", "@log_execution(fact_sales_logger)\n", "def create_date_key(df, date_column):\n", "    fact_sales_logger.info(f\"Creating date key from column: {date_column}\")\n", "    \n", "    # Create date key in format YYYYMMDD\n", "    return df.withColumn(\n", "        \"date_key\",\n", "        concat(\n", "            year(col(date_column)),\n", "            lpad(month(col(date_column)), 2, \"0\"),\n", "            lpad(dayofmonth(col(date_column)), 2, \"0\")\n", "        ).cast(\"int\")\n", "    )\n", "\n", "# Function to create fact sales table\n", "@log_execution(fact_sales_logger)\n", "def create_fact_sales(invoice_df, invoice_line_df):\n", "    fact_sales_logger.info(\"Creating fact_sales table\")\n", "    \n", "    # Add date key to invoice dataframe\n", "    if \"invoice_date\" in invoice_df.columns:\n", "        invoice_df = create_date_key(invoice_df, \"invoice_date\")\n", "    elif \"created_timestamp\" in invoice_df.columns:\n", "        invoice_df = create_date_key(invoice_df, \"created_timestamp\")\n", "    \n", "    # Join invoice and invoice_line tables\n", "    joined_df = invoice_line_df.join(\n", "        invoice_df,\n", "        invoice_line_df.invoice_id == invoice_df.invoice_id,\n", "        \"inner\"\n", "    )\n", "    \n", "    # Select columns for fact table\n", "    fact_sales = joined_df.select(\n", "        # Keys\n", "        invoice_df.invoice_id,\n", "        invoice_line_df.invoice_item_id,\n", "        invoice_line_df.item_id,\n", "        invoice_line_df.customer_id,\n", "        invoice_line_df.site_id,\n", "        invoice_line_df.tax_id,\n", "        invoice_line_df.discount_id,\n", "        \n", "        # Date dimensions\n", "        invoice_df.date_key.alias(\"invoice_date_key\"),\n", "        invoice_df.invoice_year,\n", "        invoice_df.invoice_month,\n", "        invoice_df.invoice_quarter,\n", "        \n", "        # Invoice attributes\n", "        invoice_df.invoice_status,\n", "        invoice_df.invoice_type,\n", "        invoice_df.payment_method,\n", "        invoice_df.channel_type,\n", "        invoice_df.reference_number,\n", "        \n", "        # Measures\n", "        invoice_line_df.quantity,\n", "        invoice_line_df.unit_price,\n", "        invoice_line_df.net_amount,\n", "        invoice_line_df.line_discount_amount,\n", "        invoice_line_df.line_tax_amount,\n", "        invoice_line_df.line_total_amount,\n", "        \n", "        # Data quality\n", "        invoice_line_df.data_quality_score.alias(\"line_quality_score\"),\n", "        invoice_df.data_quality_score.alias(\"invoice_quality_score\"),\n", "        \n", "        # Metadata\n", "        current_timestamp().alias(\"dw_created_at\")\n", "    )\n", "    \n", "    # Add overall quality score\n", "    fact_sales = fact_sales.withColumn(\n", "        \"data_quality_score\",\n", "        (col(\"line_quality_score\") + col(\"invoice_quality_score\")) / 2\n", "    )\n", "    \n", "    return log_dataframe_info(fact_sales, \"fact_sales\", fact_sales_logger)\n", "\n", "# Function to create sales_by_date aggregation\n", "@log_execution(fact_sales_logger)\n", "def create_sales_by_date(fact_sales):\n", "    fact_sales_logger.info(\"Creating sales_by_date aggregation\")\n", "    \n", "    sales_by_date = fact_sales.groupBy(\n", "        \"invoice_date_key\", \"invoice_year\", \"invoice_month\", \"invoice_quarter\"\n", "    ).agg(\n", "        sql_sum(\"line_total_amount\").alias(\"total_sales\"),\n", "        sql_sum(\"net_amount\").alias(\"net_sales\"),\n", "        sql_sum(\"line_discount_amount\").alias(\"total_discounts\"),\n", "        sql_sum(\"line_tax_amount\").alias(\"total_taxes\"),\n", "        count(\"invoice_item_id\").alias(\"total_items_sold\"),\n", "        count(\"invoice_id\").alias(\"total_transactions\"),\n", "        avg(\"unit_price\").alias(\"average_unit_price\"),\n", "        count(\"distinct invoice_id\").alias(\"total_invoices\"),\n", "        count(\"distinct customer_id\").alias(\"total_customers\"),\n", "        avg(\"data_quality_score\").alias(\"avg_data_quality_score\"),\n", "        current_timestamp().alias(\"dw_created_at\")\n", "    )\n", "    \n", "    # Calculate average order value\n", "    sales_by_date = sales_by_date.withColumn(\n", "        \"average_order_value\",\n", "        col(\"total_sales\") / col(\"total_invoices\")\n", "    )\n", "    \n", "    return log_dataframe_info(sales_by_date, \"sales_by_date\", fact_sales_logger)\n", "\n", "# Function to create sales_by_product aggregation\n", "@log_execution(fact_sales_logger)\n", "def create_sales_by_product(fact_sales):\n", "    fact_sales_logger.info(\"Creating sales_by_product aggregation\")\n", "    \n", "    sales_by_product = fact_sales.groupBy(\n", "        \"item_id\", \"invoice_year\", \"invoice_month\", \"invoice_quarter\"\n", "    ).agg(\n", "        sql_sum(\"line_total_amount\").alias(\"total_sales\"),\n", "        sql_sum(\"net_amount\").alias(\"net_sales\"),\n", "        sql_sum(\"line_discount_amount\").alias(\"total_discounts\"),\n", "        sql_sum(\"line_tax_amount\").alias(\"total_taxes\"),\n", "        sql_sum(\"quantity\").alias(\"total_quantity_sold\"),\n", "        count(\"invoice_id\").alias(\"total_transactions\"),\n", "        avg(\"unit_price\").alias(\"average_unit_price\"),\n", "        count(\"distinct invoice_id\").alias(\"total_invoices\"),\n", "        count(\"distinct customer_id\").alias(\"total_customers\"),\n", "        avg(\"data_quality_score\").alias(\"avg_data_quality_score\"),\n", "        current_timestamp().alias(\"dw_created_at\")\n", "    )\n", "    \n", "    return log_dataframe_info(sales_by_product, \"sales_by_product\", fact_sales_logger)\n", "\n", "# Function to write data to gold layer\n", "@log_execution(fact_sales_logger)\n", "def write_to_gold(df, table_name):\n", "    fact_sales_logger.info(f\"Writing {table_name} to gold layer\")\n", "    target_path = f\"{gold_path}/{table_name}\"\n", "    \n", "    # Create or replace the table in Unity Catalog\n", "    try:\n", "        # Check if table exists\n", "        spark.sql(f\"DESCRIBE TABLE {catalog}.{gold_schema}.{table_name}\")\n", "        table_exists = True\n", "    except AnalysisException:\n", "        table_exists = False\n", "    \n", "    # Write data using Delta format\n", "    if table_exists:\n", "        # For fact tables, we typically do a full refresh\n", "        fact_sales_logger.info(f\"Overwriting existing table {catalog}.{gold_schema}.{table_name}\")\n", "        df.write \\\n", "            .format(gold_format) \\\n", "            .option(\"mergeSchema\", \"true\") \\\n", "            .mode(\"overwrite\") \\\n", "            .saveAsTable(f\"{catalog}.{gold_schema}.{table_name}\")\n", "    else:\n", "        # Create new table\n", "        fact_sales_logger.info(f\"Creating new table {catalog}.{gold_schema}.{table_name}\")\n", "        df.write \\\n", "            .format(gold_format) \\\n", "            .option(\"mergeSchema\", \"true\") \\\n", "            .mode(\"overwrite\") \\\n", "            .saveAsTable(f\"{catalog}.{gold_schema}.{table_name}\")\n", "    \n", "    return df\n", "\n", "# Main processing function\n", "@log_execution(fact_sales_logger)\n", "def process_fact_sales():\n", "    fact_sales_logger.info(\"Processing fact_sales table\")\n", "    \n", "    # Read silver data\n", "    invoice_df, invoice_line_df = read_silver_data()\n", "    if invoice_df is None or invoice_line_df is None:\n", "        fact_sales_logger.error(\"Failed to read silver data\")\n", "        return None\n", "    \n", "    # Create fact_sales table\n", "    fact_sales_df = create_fact_sales(invoice_df, invoice_line_df)\n", "    \n", "    # Write fact_sales to gold layer\n", "    write_to_gold(fact_sales_df, \"fact_sales\")\n", "    \n", "    # Create and write sales_by_date aggregation\n", "    sales_by_date_df = create_sales_by_date(fact_sales_df)\n", "    write_to_gold(sales_by_date_df, \"sales_by_date\")\n", "    \n", "    # Create and write sales_by_product aggregation\n", "    sales_by_product_df = create_sales_by_product(fact_sales_df)\n", "    write_to_gold(sales_by_product_df, \"sales_by_product\")\n", "    \n", "    fact_sales_logger.info(\"Completed fact_sales processing\")\n", "    return fact_sales_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1db8ccea-c688-4981-84a4-819fdec4ffc3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Process fact_sales\n", "fact_sales_df = process_fact_sales()\n", "if fact_sales_df is not None:\n", "    log_dataframe_info(fact_sales_df, \"fact_sales_final\", fact_sales_logger)\n", "    display(fact_sales_df.limit(10))"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "fact_sales", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}