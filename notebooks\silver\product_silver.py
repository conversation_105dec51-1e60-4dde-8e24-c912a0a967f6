# Databricks notebook source
# MAGIC %md
# MAGIC # Product Silver Layer Processing
# MAGIC 
# MAGIC This notebook processes product data from bronze to silver layer with data quality checks and transformations.

# COMMAND ----------

# Imports
from pyspark.sql.functions import (
    col, current_timestamp, lit, when, upper, lower, trim, regexp_replace, 
    to_date, regexp_extract, datediff
)
from pyspark.sql.types import StringType

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing product silver layer processing")

# Extract frequently used config values into variables
catalog = pipeline_config["catalog"]
bronze_schema = pipeline_config["schemas"]["bronze"]
silver_schema = pipeline_config["schemas"]["silver"]
bronze_path = pipeline_config["paths"]["bronze_path"]
silver_path = pipeline_config["paths"]["silver_path"]
silver_format = pipeline_config["file_formats"]["silver"]
table_name = "dim_product"

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Configuration initialized with catalog: {catalog}, bronze_schema: {bronze_schema}, silver_schema: {silver_schema}")

# COMMAND ----------
# Schema Management Functions
@log_execution(logger)
def create_or_update_silver_table(spark, table_name, table_config, storage_path, logger):
    """Create or update Delta table with complete schema, constraints, and properties."""
    logger.info(f"Managing schema for table {table_name}")
    
    try:
        # Get configuration
        base_schema = table_config["schema"]
        primary_key = table_config.get("primary_key")
        
        # Build complete DDL with all constraints
        column_definitions = []
        for col_def in base_schema:
            col_name = col_def["name"]
            col_type = col_def["type"]
            nullable = col_def.get("nullable", True)
            
            col_ddl = f"{col_name} {col_type}"
            if not nullable:
                col_ddl += " NOT NULL"
            column_definitions.append(col_ddl)
        
        # Add audit columns
        audit_columns = [
            "created_timestamp TIMESTAMP",
            "modified_timestamp TIMESTAMP", 
            "processed_at TIMESTAMP",
            "source_system STRING",
            "data_quality_score DOUBLE",
            "primary_key_valid BOOLEAN",
            "completeness_score DOUBLE"
        ]
        
        all_columns = column_definitions + audit_columns
        schema_ddl = ",\n            ".join(all_columns)
        
        # Create complete table with constraints
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {schema_ddl},
            CONSTRAINT pk_{primary_key} PRIMARY KEY ({primary_key})
        )
        USING DELTA
        LOCATION '{storage_path}'
        TBLPROPERTIES (
            'delta.columnMapping.mode' = 'name',
            'delta.autoOptimize.optimizeWrite' = 'true',
            'delta.autoOptimize.autoCompact' = 'true',
            'delta.enableChangeDataFeed' = 'true'
        )
        """
        
        logger.info(f"Creating/updating table with DDL")
        spark.sql(create_sql)
        logger.info(f"✅ Table {table_name} ready with all constraints")
        
    except Exception as e:
        logger.error(f"Error managing table {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Simple Transformation Functions
@log_execution(logger)
def transform_data(df, table_name, table_config, logger):
    """Transform product data with all business logic in one place."""
    logger.info(f"Applying transformations for {table_name}")
    
    try:
        # Get configuration for the specific table
        config = table_config[table_name]
        column_mapping = config["column_mapping_bronze_to_silver"]
        
        # Apply column mapping
        select_expressions = []
        for bronze_col, silver_col in column_mapping.items():
            if bronze_col in df.columns:
                select_expressions.append(col(bronze_col).alias(silver_col))
        
        if select_expressions:
            df = df.select(*select_expressions)
        
        # Apply all transformations using SQL expressions for simplicity
        df = df.select(
            # Core product fields
            col("product_id"),
            trim(regexp_replace(col("product_name"), r"\s+", " ")).alias("product_name"),
            
            # Standardize product category
            when(upper(col("product_category")).isin(["GROCERY", "GROCERIES"]), "Grocery")
            .when(upper(col("product_category")).isin(["DAIRY", "DAIRY_PRODUCTS"]), "Dairy")
            .when(upper(col("product_category")).isin(["MEAT", "MEATS"]), "Meat")
            .when(upper(col("product_category")).isin(["PRODUCE", "FRUITS_VEGETABLES"]), "Produce")
            .when(upper(col("product_category")).isin(["BAKERY", "BAKED_GOODS"]), "Bakery")
            .when(upper(col("product_category")).isin(["FROZEN", "FROZEN_FOODS"]), "Frozen")
            .when(upper(col("product_category")).isin(["BEVERAGES", "DRINKS"]), "Beverages")
            .when(upper(col("product_category")).isin(["SNACKS", "SNACK_FOODS"]), "Snacks")
            .when(upper(col("product_category")).isin(["HEALTH", "HEALTH_BEAUTY"]), "Health & Beauty")
            .when(upper(col("product_category")).isin(["HOUSEHOLD", "CLEANING"]), "Household")
            .otherwise(col("product_category")).alias("product_category"),
            
            # Clean product sub category
            trim(col("product_sub_category")).alias("product_sub_category"),
            
            # Clean product description
            trim(col("product_description")).alias("product_description"),
            
            # Clean brand name
            trim(regexp_replace(col("brand_name"), r"\s+", " ")).alias("brand_name"),
            
            # Clean allergen information
            trim(col("allergen_information")).alias("allergen_information"),
            
            # Validate shelf life days
            when(col("shelf_life_days").isNotNull() & (col("shelf_life_days") >= 0), col("shelf_life_days"))
            .otherwise(None).alias("shelf_life_days"),
            
            # Convert boolean fields
            when(upper(col("is_private_label")).isin(["TRUE", "1", "Y", "YES"]), True)
            .when(upper(col("is_private_label")).isin(["FALSE", "0", "N", "NO"]), False)
            .otherwise(None).alias("is_private_label"),
            
            when(upper(col("is_organic")).isin(["TRUE", "1", "Y", "YES"]), True)
            .when(upper(col("is_organic")).isin(["FALSE", "0", "N", "NO"]), False)
            .otherwise(None).alias("is_organic"),
            
            when(upper(col("is_perishable")).isin(["TRUE", "1", "Y", "YES"]), True)
            .when(upper(col("is_perishable")).isin(["FALSE", "0", "N", "NO"]), False)
            .otherwise(None).alias("is_perishable"),
            
            when(upper(col("is_active")).isin(["TRUE", "1", "ACTIVE", "Y", "YES"]), True)
            .when(upper(col("is_active")).isin(["FALSE", "0", "INACTIVE", "N", "NO"]), False)
            .otherwise(None).alias("is_active"),
            
            # Add validation columns
            when(col("shelf_life_days").isNotNull(),
                 when(col("shelf_life_days") >= 0, True).otherwise(False)
            ).otherwise(None).alias("shelf_life_valid"),
            
            when(col("allergen_information").isNotNull() & (col("allergen_information") != ""),
                 True
            ).otherwise(False).alias("has_allergen_info"),
            
            # Data quality validation columns
            when(col("product_id").isNotNull() & (col("product_id") != ""), True).otherwise(False).alias("primary_key_valid"),
            when(col("product_id").isNotNull() & (col("product_id") != ""), True).otherwise(False).alias("product_id_not_null_valid"),
            when(col("product_name").isNotNull() & (col("product_name") != ""), True).otherwise(False).alias("product_name_not_null_valid"),
            when(col("product_category").isNotNull() & (col("product_category") != ""), True).otherwise(False).alias("product_category_not_null_valid"),
            
            # Calculate completeness score based on required fields
            ((when(col("product_id").isNotNull() & (col("product_id") != ""), 1).otherwise(0) +
              when(col("product_name").isNotNull() & (col("product_name") != ""), 1).otherwise(0) +
              when(col("product_category").isNotNull() & (col("product_category") != ""), 1).otherwise(0)) / 3.0).alias("completeness_score"),
            
            # Calculate overall data quality score
            ((when(col("product_id").isNotNull() & (col("product_id") != ""), 1).otherwise(0) +
              when(col("product_name").isNotNull() & (col("product_name") != ""), 1).otherwise(0) +
              when(col("product_category").isNotNull() & (col("product_category") != ""), 1).otherwise(0) +
              when(col("brand_name").isNotNull() & (col("brand_name") != ""), 1).otherwise(0)) / 4.0).alias("data_quality_score"),
            
            # Add audit columns
            current_timestamp().alias("created_timestamp"),
            current_timestamp().alias("modified_timestamp"),
            current_timestamp().alias("processed_at"),
            lit("bronze_layer").alias("source_system")
        )
        
        logger.info(f"Transformation completed. Columns: {len(df.columns)}")
        return df
        
    except Exception as e:
        logger.error(f"Error in transformation: {str(e)}")
        raise

# COMMAND ----------
# Data I/O Functions
@log_execution(logger)
def read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date=None):
    """Read data from bronze layer."""
    logger.info(f"Reading bronze data for {table_name}")
    
    try:
        if processing_date:
            date_path = processing_date.replace('-', '/')
            source_path = f"{bronze_path}/{table_name.replace('dim_', '')}/{date_path}"
            df = spark.read.format("parquet").load(source_path)
        else:
            source_path = f"{catalog}.{bronze_schema}.{table_name.replace('dim_', '')}"
            df = spark.table(source_path)
        
        logger.info(f"Read {df.count()} records from bronze for {table_name}")
        return df
        
    except Exception as e:
        logger.error(f"Error reading bronze data for {table_name}: {str(e)}")
        raise

@log_execution(logger)
def write_silver_data(spark, df, table_name, table_config, catalog, silver_schema, silver_path, logger):
    """Write data to silver layer with proper schema management."""
    logger.info(f"Writing data to silver table {table_name}")
    
    try:
        # Get configuration for the specific table
        config = table_config[table_name]
        primary_key = config["primary_key"]
        
        # Construct paths
        full_table_name = f"{catalog}.{silver_schema}.{table_name}"
        target_path = f"{silver_path}/{table_name}"
        
        # Create or update table schema
        logger.info("Ensuring table schema is up to date")
        create_or_update_silver_table(spark, full_table_name, config, target_path, logger)
        
        # Write data using merge for upsert behavior
        logger.info("Writing data with upsert logic")
        
        # Create temporary view for merge
        temp_view_name = f"temp_{table_name}_data"
        df.createOrReplaceTempView(temp_view_name)
        
        # Use SQL MERGE for clean upsert
        merge_sql = f"""
        MERGE INTO {full_table_name} AS target
        USING {temp_view_name} AS source
        ON target.{primary_key} = source.{primary_key}
        WHEN MATCHED THEN UPDATE SET *
        WHEN NOT MATCHED THEN INSERT *
        """
        
        spark.sql(merge_sql)
        logger.info("✅ Data written successfully with upsert")
        
        # Optimize table
        spark.sql(f"OPTIMIZE {full_table_name}")
        logger.info("✅ Table optimized")
        
        return df
        
    except Exception as e:
        logger.error(f"Error writing silver data for {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main Processing Function
@log_execution(logger)
def process_bronze_to_silver(spark, table_name, catalog, bronze_schema, silver_schema, 
                           bronze_path, silver_path, logger, processing_date=None):
    """Generic function to process any table from bronze to silver layer."""
    logger.info(f"🚀 Starting bronze to silver processing for {table_name}")
    
    try:
        # Get table configuration using table name
        table_config_data = table_config[table_name]
        logger.info(f"Loaded configuration for {table_name}")
        
        # Step 1: Read bronze data
        bronze_df = read_bronze_data(spark, table_name, catalog, bronze_schema, bronze_path, logger, processing_date)
        if bronze_df.count() == 0:
            logger.warning(f"No bronze data found for {table_name}")
            return None
        
        # Step 2: Transform data
        silver_df = transform_data(bronze_df, table_name, {table_name: table_config_data}, logger)
        
        # Step 3: Write to silver layer
        write_silver_data(spark, silver_df, table_name, {table_name: table_config_data}, catalog, silver_schema, silver_path, logger)
        
        logger.info(f"✅ Successfully processed {silver_df.count()} records for {table_name}")
        return silver_df
        
    except Exception as e:
        logger.error(f"❌ Error processing {table_name}: {str(e)}")
        raise

# COMMAND ----------
# Main execution
logger.info(f"Starting bronze to silver processing for {table_name}")

# Get processing date from configuration
processing_date = pipeline_config.get("default_processing_date")

# Process data using configuration
try:
    result_df = process_bronze_to_silver(
        spark=spark,
        table_name=table_name,
        catalog=catalog,
        bronze_schema=bronze_schema,
        silver_schema=silver_schema,
        bronze_path=bronze_path,
        silver_path=silver_path,
        logger=logger,
        processing_date=processing_date
    )
    if result_df is not None:
        logger.info(f"✅ Completed bronze to silver processing successfully for {table_name}")
        log_dataframe_info(result_df, f"{table_name}_silver_final", logger)
    else:
        logger.warning(f"⚠️ No data processed for {table_name}")
except Exception as e:
    logger.error(f"❌ Failed to process {table_name}: {str(e)}")
    raise
