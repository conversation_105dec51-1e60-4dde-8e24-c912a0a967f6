# Databricks notebook source
# MAGIC %md
# MAGIC # Column Masking Functions
# MAGIC 
# MAGIC This notebook contains SQL-based column masking functions for data security and privacy.
# MAGIC Functions are applied based on current_user() permissions and table configurations.

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup and Configuration

# COMMAND ----------

# Imports
from pyspark.sql import SparkSession
from pyspark.sql.functions import current_user

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing column masking functions")

# Extract configuration values
catalog = pipeline_config["catalog"]
silver_schema = pipeline_config["schemas"]["silver"]

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Column masking initialized for catalog: {catalog}, schema: {silver_schema}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Email Masking Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create email masking function
# MAGIC CREATE OR REPLACE FUNCTION mask_email(email STRING, user_email STRING)
# MAGIC RETURNS STRING
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Masks email addresses based on user permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN 
# MAGIC       CASE 
# MAGIC         WHEN email IS NULL THEN NULL
# MAGIC         WHEN email = '' THEN ''
# MAGIC         ELSE CONCAT(
# MAGIC           LEFT(SPLIT(email, '@')[0], 2),
# MAGIC           REPEAT('*', GREATEST(0, LENGTH(SPLIT(email, '@')[0]) - 2)),
# MAGIC           '@',
# MAGIC           SPLIT(email, '@')[1]
# MAGIC         )
# MAGIC       END
# MAGIC     ELSE email  -- Full access for other users
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Phone Number Masking Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create phone number masking function
# MAGIC CREATE OR REPLACE FUNCTION mask_phone(phone STRING, user_email STRING)
# MAGIC RETURNS STRING
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Masks phone numbers based on user permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN 
# MAGIC       CASE 
# MAGIC         WHEN phone IS NULL THEN NULL
# MAGIC         WHEN phone = '' THEN ''
# MAGIC         WHEN LENGTH(phone) >= 10 THEN CONCAT(
# MAGIC           LEFT(phone, 3),
# MAGIC           REPEAT('*', LENGTH(phone) - 6),
# MAGIC           RIGHT(phone, 3)
# MAGIC         )
# MAGIC         ELSE REPEAT('*', LENGTH(phone))
# MAGIC       END
# MAGIC     ELSE phone  -- Full access for other users
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Address Masking Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create address masking function
# MAGIC CREATE OR REPLACE FUNCTION mask_address(address STRING, user_email STRING)
# MAGIC RETURNS STRING
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Masks street addresses while preserving city and postal code'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN 
# MAGIC       CASE 
# MAGIC         WHEN address IS NULL THEN NULL
# MAGIC         WHEN address = '' THEN ''
# MAGIC         ELSE CONCAT(
# MAGIC           '*** MASKED ***',
# MAGIC           CASE 
# MAGIC             WHEN address RLIKE '.*,.*,.*' THEN 
# MAGIC               CONCAT(', ', 
# MAGIC                 SUBSTRING(address, LOCATE(',', address, LOCATE(',', address) + 1))
# MAGIC               )
# MAGIC             ELSE ''
# MAGIC           END
# MAGIC         )
# MAGIC       END
# MAGIC     ELSE address  -- Full access for other users
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Customer ID Masking Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create customer ID masking function
# MAGIC CREATE OR REPLACE FUNCTION mask_customer_id(customer_id STRING, user_email STRING)
# MAGIC RETURNS STRING
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Masks customer IDs for restricted users'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN 
# MAGIC       CASE 
# MAGIC         WHEN customer_id IS NULL THEN NULL
# MAGIC         WHEN customer_id = '' THEN ''
# MAGIC         WHEN LENGTH(customer_id) > 4 THEN CONCAT(
# MAGIC           LEFT(customer_id, 2),
# MAGIC           REPEAT('*', LENGTH(customer_id) - 4),
# MAGIC           RIGHT(customer_id, 2)
# MAGIC         )
# MAGIC         ELSE REPEAT('*', LENGTH(customer_id))
# MAGIC       END
# MAGIC     ELSE customer_id  -- Full access for other users
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Loyalty Card ID Masking Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create loyalty card ID masking function
# MAGIC CREATE OR REPLACE FUNCTION mask_loyalty_card_id(loyalty_card_id STRING, user_email STRING)
# MAGIC RETURNS STRING
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Masks loyalty card IDs for restricted users'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN 
# MAGIC       CASE 
# MAGIC         WHEN loyalty_card_id IS NULL THEN NULL
# MAGIC         WHEN loyalty_card_id = '' THEN ''
# MAGIC         ELSE CONCAT('****-****-', RIGHT(loyalty_card_id, 4))
# MAGIC       END
# MAGIC     ELSE loyalty_card_id  -- Full access for other users
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Generic Masking Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create generic string masking function
# MAGIC CREATE OR REPLACE FUNCTION mask_string(input_string STRING, user_email STRING, mask_char STRING DEFAULT '*')
# MAGIC RETURNS STRING
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Generic string masking function'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN 
# MAGIC       CASE 
# MAGIC         WHEN input_string IS NULL THEN NULL
# MAGIC         WHEN input_string = '' THEN ''
# MAGIC         ELSE REPEAT(mask_char, LENGTH(input_string))
# MAGIC       END
# MAGIC     ELSE input_string  -- Full access for other users
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Apply Column Masks to Tables

# COMMAND ----------

# Python function to apply column masks based on table configuration
def apply_column_masks_to_table(table_name, logger):
    """Apply column masking to a specific table based on configuration."""
    logger.info(f"Applying column masks to {table_name}")
    
    try:
        # Get table configuration
        if table_name not in table_config:
            logger.warning(f"No configuration found for table {table_name}")
            return
        
        config = table_config[table_name]
        schema_def = config.get("schema", [])
        
        # Build masked view SQL
        masked_columns = []
        
        for col_def in schema_def:
            col_name = col_def["name"]
            col_type = col_def.get("type", "STRING")
            
            # Apply masking based on column name and type
            if col_name == "customer_email_address":
                masked_columns.append(f"mask_email({col_name}, current_user()) AS {col_name}")
            elif col_name == "phone_number":
                masked_columns.append(f"mask_phone({col_name}, current_user()) AS {col_name}")
            elif col_name == "address":
                masked_columns.append(f"mask_address({col_name}, current_user()) AS {col_name}")
            elif col_name == "customer_id":
                masked_columns.append(f"mask_customer_id({col_name}, current_user()) AS {col_name}")
            elif col_name == "loyalty_card_id":
                masked_columns.append(f"mask_loyalty_card_id({col_name}, current_user()) AS {col_name}")
            else:
                # No masking for other columns
                masked_columns.append(col_name)
        
        # Create masked view
        columns_sql = ",\n    ".join(masked_columns)
        view_name = f"{table_name}_masked"
        
        create_view_sql = f"""
        CREATE OR REPLACE VIEW {view_name} AS
        SELECT 
            {columns_sql}
        FROM {table_name}
        """
        
        logger.info(f"Creating masked view: {view_name}")
        spark.sql(create_view_sql)
        logger.info(f"✅ Successfully created masked view {view_name}")
        
    except Exception as e:
        logger.error(f"Error applying column masks to {table_name}: {str(e)}")
        raise

# COMMAND ----------

# MAGIC %md
# MAGIC ## Main Execution

# COMMAND ----------

# Apply column masking to configured tables
logger.info("Starting column masking application")

try:
    # Apply masking to customer table
    apply_column_masks_to_table("dim_customer", logger)
    
    # Add other tables as needed
    # apply_column_masks_to_table("dim_product", logger)
    # apply_column_masks_to_table("fact_sales", logger)
    
    logger.info("✅ Column masking application completed successfully")
    
except Exception as e:
    logger.error(f"❌ Failed to apply column masking: {str(e)}")
    raise

# COMMAND ----------

# MAGIC %md
# MAGIC ## Testing Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Test the masking functions
# MAGIC SELECT 
# MAGIC   mask_email('<EMAIL>', '<EMAIL>') AS masked_email,
# MAGIC   mask_phone('************', '<EMAIL>') AS masked_phone,
# MAGIC   mask_address('123 Main St, Toronto, ON M5V 3A8', '<EMAIL>') AS masked_address,
# MAGIC   mask_customer_id('CUST123456', '<EMAIL>') AS masked_customer_id,
# MAGIC   mask_loyalty_card_id('1234-5678-9012', '<EMAIL>') AS masked_loyalty_card
