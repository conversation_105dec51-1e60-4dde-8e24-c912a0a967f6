{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "13b1f99c-0d20-49cf-93fe-c160c795e4a0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Enhanced Logger Utility with Color Support\n", "\n", "This notebook provides logging functionality for the data pipeline using Python's built-in logging module with color-coded output for better readability."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "72ed5a87-d00c-4da8-b5dc-2ed786ad684e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import logging\n", "import os\n", "import sys\n", "import inspect\n", "import functools\n", "import time\n", "from pyspark.sql import SparkSession\n", "\n", "# Import log_path from config\n", "try:\n", "    from config.config import log_path\n", "except ImportError:\n", "    # Fallback if import fails\n", "    log_path = None\n", "\n", "# Get the Spark session\n", "spark = SparkSession.builder.getOrCreate()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "602e94b1-9f3b-407d-ace3-1b56f5bbd0c4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_current_notebook_name():\n", "    \"\"\"\n", "    Automatically detect the current notebook name from the Databricks context.\n", "    Returns the notebook name without the file extension.\n", "    \"\"\"\n", "    try:\n", "        # First try to get the notebook path from Databricks context\n", "        # Check if we're in a Databricks environment\n", "        if 'dbutils' in locals() or 'dbutils' in globals():\n", "            notebook_path = dbutils.notebook.entry_point.getDbutils().notebook().getContext().notebookPath().get()\n", "            return os.path.basename(notebook_path).split('.')[0]\n", "        \n", "        # If not in Databricks, try to get the calling module's filename\n", "        frame = inspect.currentframe()\n", "        while frame:\n", "            if frame.f_code.co_filename != __file__:\n", "                caller_file = frame.f_code.co_filename\n", "                return os.path.basename(caller_file).split('.')[0]\n", "            frame = frame.f_back\n", "        \n", "        # If all else fails, try to get the main module's filename\n", "        main_module = sys.modules['__main__'].__file__\n", "        if main_module:\n", "            return os.path.basename(main_module).split('.')[0]\n", "            \n", "        return \"unknown_notebook\"\n", "    except Exception as e:\n", "        # Fallback in case of any issues\n", "        print(f\"Warning: Could not automatically detect notebook name: {str(e)}\")\n", "        return \"unknown_notebook\""]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "977f0647-3d56-40c9-8b51-0c0440d0f87d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["class NotebookFormatter(logging.Formatter):\n", "    \"\"\"\n", "    Custom formatter that includes notebook name in the log message.\n", "    \"\"\"\n", "    def __init__(self, notebook_name, fmt=None, datefmt=None, style='%'):\n", "        if fmt is None:\n", "            fmt = \"[%(asctime)s] [%(levelname)s] [\" + notebook_name + \"] [%(name)s] %(message)s\"\n", "        super().__init__(fmt, datefmt, style)\n", "\n", "class ColoredNotebookFormatter(logging.Formatter):\n", "    \"\"\"\n", "    Custom formatter that includes notebook name in the log message with color coding.\n", "    \"\"\"\n", "    # ANSI color codes using Unicode escape sequences\n", "    COLORS = {\n", "        'RESET': '\\u001b[0m',\n", "        'BLACK': '\\u001b[30m',\n", "        'RED': '\\u001b[31m',\n", "        'GREEN': '\\u001b[32m',\n", "        'YELLOW': '\\u001b[33m',\n", "        'BLUE': '\\u001b[34m',\n", "        'MAGENTA': '\\u001b[35m',\n", "        'CYAN': '\\u001b[36m',\n", "        'WHITE': '\\u001b[37m',\n", "        'BOLD': '\\u001b[1m',\n", "        'UNDERLINE': '\\u001b[4m',\n", "        'BACKGROUND_BLACK': '\\u001b[40m',\n", "        'BACKGROUND_RED': '\\u001b[41m',\n", "        'BACKGROUND_GREEN': '\\u001b[42m',\n", "        'BACKGROUND_YELLOW': '\\u001b[43m',\n", "        'BACKGROUND_BLUE': '\\u001b[44m',\n", "        'BACKGROUND_MAGENTA': '\\u001b[45m',\n", "        'BACKGROUND_CYAN': '\\u001b[46m',\n", "        'BACKGROUND_WHITE': '\\u001b[47m',\n", "    }\n", "    \n", "    # Log level colors\n", "    LEVEL_COLORS = {\n", "        'DEBUG': COLORS['BLUE'],\n", "        'INFO': COLORS['GREEN'],\n", "        'WARNING': COLORS['YELLOW'],\n", "        'ERROR': COLORS['RED'],\n", "        'CRITICAL': COLORS['RED'] + COLORS['BOLD'],\n", "    }\n", "    \n", "    # Component colors\n", "    COMPONENT_COLORS = {\n", "        'data_loading': COLORS['CYAN'],\n", "        'transformation': COLORS['MAGENTA'],\n", "        'data_writing': COLORS['YELLOW'],\n", "    }\n", "    \n", "    def __init__(self, notebook_name, fmt=None, datefmt=None, style='%', use_colors=True):\n", "        if fmt is None:\n", "            fmt = \"[%(asctime)s] [%(levelname)s] [\" + notebook_name + \"] [%(name)s] %(message)s\"\n", "        self.use_colors = use_colors\n", "        self.notebook_name = notebook_name\n", "        super().__init__(fmt, datefmt, style)\n", "        \n", "    def format(self, record):\n", "        if not self.use_colors:\n", "            return super().format(record)\n", "            \n", "        # Save the original format\n", "        original_fmt = self._style._fmt\n", "        \n", "        # Apply color based on log level\n", "        levelname = record.levelname\n", "        level_color = self.LEVEL_COLORS.get(levelname, self.COLORS['RESET'])\n", "        \n", "        # Check if this is a component logger\n", "        component_color = self.COLORS['RESET']\n", "        if '.' in record.name:\n", "            component = record.name.split('.')[-1]\n", "            component_color = self.COMPONENT_COLORS.get(component, self.COLORS['RESET'])\n", "        \n", "        # Apply colors to different parts of the log message\n", "        colored_fmt = original_fmt.replace(\n", "            \"[%(asctime)s]\", \n", "            f\"{self.COLORS['CYAN']}[%(asctime)s]{self.COLORS['RESET']}\"\n", "        )\n", "        \n", "        colored_fmt = colored_fmt.replace(\n", "            \"[%(levelname)s]\", \n", "            f\"{level_color}[%(levelname)s]{self.COLORS['RESET']}\"\n", "        )\n", "        \n", "        colored_fmt = colored_fmt.replace(\n", "            f\"[{self.notebook_name}]\", \n", "            f\"{self.COLORS['BOLD']}[{self.notebook_name}]{self.COLORS['RESET']}\"\n", "        )\n", "        \n", "        # Apply color to component in the name\n", "        if '.' in record.name:\n", "            base_name, component = record.name.rsplit('.', 1)\n", "            colored_fmt = colored_fmt.replace(\n", "                f\"[%(name)s]\", \n", "                f\"[{base_name}.{component_color}{component}{self.COLORS['RESET']}]\"\n", "            )\n", "        \n", "        # Set the colored format\n", "        self._style._fmt = colored_fmt\n", "        \n", "        # Format the record with colors\n", "        result = super().format(record)\n", "        \n", "        # Restore the original format\n", "        self._style._fmt = original_fmt\n", "        \n", "        return result"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "ad2c95fa-a388-4ffc-97e7-6e30e395f4e3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def create_logger(notebook_name=None, log_level=\"INFO\", log_file=None, component_log_levels=None, custom_log_path=None):\n", "    \"\"\"\n", "    Create a logger for a notebook using Python's built-in logging module.\n", "    \n", "    Args:\n", "        notebook_name: Name of the notebook (if None, auto-detected)\n", "        log_level: Default log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)\n", "        log_file: Optional path to a log file. If None and custom_log_path is None, \n", "                 will use the log_path from config if available\n", "        component_log_levels: Optional dict mapping component names to log levels\n", "                             e.g., {\"data_loading\": \"DEBUG\", \"transformation\": \"INFO\"}\n", "        custom_log_path: Optional custom path to store logs. Overrides the default log_path from config\n", "    \n", "    Returns:\n", "        A configured logger instance\n", "    \"\"\"\n", "    # Auto-detect notebook name if not provided\n", "    if notebook_name is None:\n", "        notebook_name = get_current_notebook_name()\n", "    \n", "    # Convert string log level to logging constant\n", "    numeric_level = getattr(logging, log_level.upper(), None)\n", "    if not isinstance(numeric_level, int):\n", "        raise ValueError(f\"Invalid log level: {log_level}\")\n", "    \n", "    # Create logger\n", "    logger = logging.getLogger(notebook_name)\n", "    logger.setLevel(numeric_level)\n", "    logger.handlers = []  # Remove any existing handlers\n", "    \n", "    # Create console handler with colored formatter\n", "    console_handler = logging.StreamHandler()\n", "    console_handler.setLevel(numeric_level)\n", "    console_formatter = ColoredNotebookFormatter(notebook_name, use_colors=True)\n", "    console_handler.setFormatter(console_formatter)\n", "    logger.addHandler(console_handler)\n", "    \n", "    # Determine log file path if not explicitly provided\n", "    if not log_file:\n", "        # Use custom_log_path if provided, otherwise use log_path from config\n", "        base_log_path = custom_log_path or log_path\n", "        \n", "        if base_log_path:\n", "            # Create a log file name based on the notebook name\n", "            log_file = f\"{base_log_path}/{notebook_name}.log\"\n", "    \n", "    # Create file handler if log_file is specified\n", "    if log_file:\n", "        # Create directory if it doesn't exist\n", "        log_dir = os.path.dirname(log_file)\n", "        if log_dir and not os.path.exists(log_dir):\n", "            os.makedirs(log_dir, exist_ok=True)\n", "            \n", "        file_handler = logging.FileHandler(log_file)\n", "        file_handler.setLevel(numeric_level)\n", "        # Use regular formatter for file logs (no colors in files)\n", "        file_formatter = NotebookFormatter(notebook_name)\n", "        file_handler.setFormatter(file_formatter)\n", "        # logger.addHandler(file_handler)\n", "    \n", "    # Set up component loggers if specified\n", "    if component_log_levels:\n", "        for component, level in component_log_levels.items():\n", "            component_logger = logging.getLogger(f\"{notebook_name}.{component}\")\n", "            component_numeric_level = getattr(logging, level.upper(), None)\n", "            if isinstance(component_numeric_level, int):\n", "                component_logger.setLevel(component_numeric_level)\n", "    \n", "    # Add convenience methods for logging execution start/end with enhanced styling\n", "    def log_start(operation, component=None):\n", "        start_msg = \"▶️ Starting \" + str(operation)\n", "        if component:\n", "            logging.getLogger(f\"{notebook_name}.{component}\").info(start_msg)\n", "        else:\n", "            logger.info(start_msg)\n", "    \n", "    def log_end(operation, duration=None, component=None):\n", "        if duration is not None:\n", "            duration_str = f\" (Duration: {duration:.2f}s)\"\n", "        else:\n", "            duration_str = \"\"\n", "        end_msg = \"✅ Completed \" + str(operation) + duration_str\n", "        if component:\n", "            logging.getLogger(f\"{notebook_name}.{component}\").info(end_msg)\n", "        else:\n", "            logger.info(end_msg)\n", "    \n", "    # Attach convenience methods to the logger\n", "    logger.log_start = log_start\n", "    logger.log_end = log_end\n", "    \n", "    return logger"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "8d527e2b-4928-4b36-95cf-f5d3e2cc52a7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def log_execution(logger):\n", "    \"\"\"\n", "    Decorator factory to log function execution time and status.\n", "    \n", "    Args:\n", "        logger: The logger instance to use for logging\n", "    \"\"\"\n", "    def decorator(func):\n", "        @functools.wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            function_name = func.__name__\n", "            \n", "            # Try to determine the component from the function name\n", "            component = None\n", "            if \"load\" in function_name or \"read\" in function_name:\n", "                component = \"data_loading\"\n", "            elif \"transform\" in function_name:\n", "                component = \"transformation\"\n", "            elif \"write\" in function_name or \"save\" in function_name or \"upsert\" in function_name:\n", "                component = \"data_writing\"\n", "            \n", "            # Get the appropriate logger\n", "            log = logger\n", "            if component:\n", "                component_logger = logging.getLogger(f\"{logger.name}.{component}\")\n", "                if component_logger.level != logging.NOTSET:  # If component logger has a specific level\n", "                    log = component_logger\n", "            \n", "            # Format arguments for logging\n", "            arg_list = [str(a) for a in args[1:]] if len(args) > 1 else []\n", "            kwarg_list = [f\"{k}={v}\" for k, v in kwargs.items()]\n", "            arg_str = \", \".join(arg_list + kwarg_list)\n", "            \n", "            log.info(f\"🔄 Executing {function_name}({arg_str})\")\n", "            start = time.perf_counter()\n", "            try:\n", "                result = func(*args, **kwargs)\n", "                duration = time.perf_counter() - start\n", "                log.info(f\"✅ Successfully executed {function_name} in {round(duration, 2)}s\")\n", "                return result\n", "            except Exception as e:\n", "                duration = time.perf_counter() - start\n", "                log.error(f\"❌ Error in {function_name} after {round(duration, 2)}s: {str(e)}\")\n", "                raise\n", "        return wrapper\n", "    return decorator"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "67caf701-88d1-4d6b-a103-11f8f6c1f865", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def log_dataframe_info(df, name, logger, component=None):\n", "    \"\"\"\n", "    Log information about a DataFrame.\n", "    \n", "    Args:\n", "        df: The DataFrame to log information about\n", "        name: A name to identify the DataFrame in logs\n", "        logger: The logger instance to use for logging\n", "        component: Optional component name for component-specific logging\n", "    \"\"\"\n", "    # Get the appropriate logger\n", "    log = logger\n", "    if component:\n", "        component_logger = logging.getLogger(f\"{logger.name}.{component}\")\n", "        if component_logger.level != logging.NOTSET:  # If component logger has a specific level\n", "            log = component_logger\n", "    \n", "    count = df.count()\n", "    columns = len(df.columns)\n", "    log.info(f\"📊 DataFrame '{name}' has {count} rows and {columns} columns\")\n", "    \n", "    # Log schema details at debug level\n", "    schema_str = \"\\n  \" + \"\\n  \".join([f\"{field.name}: {field.dataType}\" for field in df.schema.fields])\n", "    log.debug(f\"🔍 DataFrame '{name}' schema: {schema_str}\")\n", "    \n", "    # Log sample data at debug level\n", "    if log.isEnabledFor(logging.DEBUG) and count > 0:\n", "        try:\n", "            sample = df.limit(5).to<PERSON><PERSON><PERSON>()\n", "            log.debug(f\"🔍 DataFrame '{name}' sample data:\\n{sample}\")\n", "        except Exception as e:\n", "            log.debug(f\"Could not convert sample to pandas: {str(e)}\")\n", "    \n", "    return df"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": null, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "logger", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}