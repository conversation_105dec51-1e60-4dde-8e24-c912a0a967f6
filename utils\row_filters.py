# Databricks notebook source
# MAGIC %md
# MAGIC # Row Filtering Functions
# MAGIC 
# MAGIC This notebook contains SQL-based row filtering functions for data security and access control.
# MAGIC Functions are applied based on current_user() permissions and table configurations.

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup and Configuration

# COMMAND ----------

# Imports
from pyspark.sql import SparkSession
from pyspark.sql.functions import current_user

# Initialize logger using configuration from startup
logger = create_logger(component_log_levels=component_log_levels)
logger.info("🚀 Initializing row filtering functions")

# Extract configuration values
catalog = pipeline_config["catalog"]
silver_schema = pipeline_config["schemas"]["silver"]

# Switch to catalog/schema
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {silver_schema}")

logger.info(f"Row filtering initialized for catalog: {catalog}, schema: {silver_schema}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## User Permission Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create function to check if user has full access
# MAGIC CREATE OR REPLACE FUNCTION has_full_access(user_email STRING)
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Checks if user has full data access permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email != '<EMAIL>' THEN TRUE
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create function to check if user has restricted access
# MAGIC CREATE OR REPLACE FUNCTION has_restricted_access(user_email STRING)
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Checks if user has restricted data access permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN user_email = '<EMAIL>' THEN TRUE
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Customer Data Row Filters

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create customer row filter function
# MAGIC CREATE OR REPLACE FUNCTION filter_customer_rows(
# MAGIC   customer_type STRING,
# MAGIC   is_active BOOLEAN,
# MAGIC   registration_date DATE,
# MAGIC   user_email STRING
# MAGIC )
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Filters customer rows based on user permissions and business rules'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN has_full_access(user_email) THEN TRUE
# MAGIC     WHEN has_restricted_access(user_email) THEN 
# MAGIC       CASE 
# MAGIC         -- Restricted users can only see active individual customers
# MAGIC         WHEN customer_type = 'Individual' 
# MAGIC           AND is_active = TRUE 
# MAGIC           AND registration_date >= DATE_SUB(CURRENT_DATE(), 365) -- Last 1 year only
# MAGIC           THEN TRUE
# MAGIC         ELSE FALSE
# MAGIC       END
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Date-based Row Filters

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create date range filter function
# MAGIC CREATE OR REPLACE FUNCTION filter_by_date_range(
# MAGIC   record_date DATE,
# MAGIC   user_email STRING,
# MAGIC   days_back INT DEFAULT 365
# MAGIC )
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Filters rows based on date range and user permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN has_full_access(user_email) THEN TRUE
# MAGIC     WHEN has_restricted_access(user_email) THEN 
# MAGIC       CASE 
# MAGIC         WHEN record_date >= DATE_SUB(CURRENT_DATE(), days_back) THEN TRUE
# MAGIC         ELSE FALSE
# MAGIC       END
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Geographic Row Filters

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create geographic filter function
# MAGIC CREATE OR REPLACE FUNCTION filter_by_geography(
# MAGIC   province STRING,
# MAGIC   user_email STRING
# MAGIC )
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Filters rows based on geographic location and user permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN has_full_access(user_email) THEN TRUE
# MAGIC     WHEN has_restricted_access(user_email) THEN 
# MAGIC       CASE 
# MAGIC         -- Restricted users can only see Ontario and Quebec data
# MAGIC         WHEN province IN ('ON', 'QC') THEN TRUE
# MAGIC         ELSE FALSE
# MAGIC       END
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Row Filters

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create data quality filter function
# MAGIC CREATE OR REPLACE FUNCTION filter_by_data_quality(
# MAGIC   data_quality_score DOUBLE,
# MAGIC   primary_key_valid BOOLEAN,
# MAGIC   user_email STRING
# MAGIC )
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Filters rows based on data quality and user permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN has_full_access(user_email) THEN TRUE
# MAGIC     WHEN has_restricted_access(user_email) THEN 
# MAGIC       CASE 
# MAGIC         -- Restricted users can only see high quality data
# MAGIC         WHEN data_quality_score >= 0.8 
# MAGIC           AND primary_key_valid = TRUE 
# MAGIC           THEN TRUE
# MAGIC         ELSE FALSE
# MAGIC       END
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Business Rule Row Filters

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create business rule filter function
# MAGIC CREATE OR REPLACE FUNCTION filter_by_business_rules(
# MAGIC   customer_type STRING,
# MAGIC   is_active BOOLEAN,
# MAGIC   loyalty_card_id STRING,
# MAGIC   user_email STRING
# MAGIC )
# MAGIC RETURNS BOOLEAN
# MAGIC LANGUAGE SQL
# MAGIC DETERMINISTIC
# MAGIC COMMENT 'Filters rows based on business rules and user permissions'
# MAGIC RETURN 
# MAGIC   CASE 
# MAGIC     WHEN has_full_access(user_email) THEN TRUE
# MAGIC     WHEN has_restricted_access(user_email) THEN 
# MAGIC       CASE 
# MAGIC         -- Restricted users can only see active customers with loyalty cards
# MAGIC         WHEN is_active = TRUE 
# MAGIC           AND loyalty_card_id IS NOT NULL 
# MAGIC           AND loyalty_card_id != ''
# MAGIC           THEN TRUE
# MAGIC         ELSE FALSE
# MAGIC       END
# MAGIC     ELSE FALSE
# MAGIC   END;

# COMMAND ----------

# MAGIC %md
# MAGIC ## Apply Row Filters to Tables

# COMMAND ----------

# Python function to apply row filters based on table configuration
def apply_row_filters_to_table(table_name, logger):
    """Apply row filtering to a specific table based on configuration."""
    logger.info(f"Applying row filters to {table_name}")
    
    try:
        # Get table configuration
        if table_name not in table_config:
            logger.warning(f"No configuration found for table {table_name}")
            return
        
        config = table_config[table_name]
        schema_def = config.get("schema", [])
        
        # Build column list
        columns = [col_def["name"] for col_def in schema_def]
        columns_sql = ", ".join(columns)
        
        # Build filter conditions based on table type
        filter_conditions = []
        
        if table_name == "dim_customer":
            # Customer-specific filters
            filter_conditions.extend([
                "filter_customer_rows(customer_type, is_active, registration_date, current_user())",
                "filter_by_date_range(registration_date, current_user(), 365)",
                "filter_by_geography(province, current_user())",
                "filter_by_data_quality(data_quality_score, primary_key_valid, current_user())",
                "filter_by_business_rules(customer_type, is_active, loyalty_card_id, current_user())"
            ])
        
        # Combine all filter conditions
        where_clause = " AND ".join(filter_conditions) if filter_conditions else "TRUE"
        
        # Create filtered view
        view_name = f"{table_name}_filtered"
        
        create_view_sql = f"""
        CREATE OR REPLACE VIEW {view_name} AS
        SELECT 
            {columns_sql}
        FROM {table_name}
        WHERE {where_clause}
        """
        
        logger.info(f"Creating filtered view: {view_name}")
        logger.debug(f"Filter conditions: {where_clause}")
        spark.sql(create_view_sql)
        logger.info(f"✅ Successfully created filtered view {view_name}")
        
    except Exception as e:
        logger.error(f"Error applying row filters to {table_name}: {str(e)}")
        raise

# COMMAND ----------

# Python function to create combined masked and filtered view
def create_secure_view(table_name, logger):
    """Create a view that combines both column masking and row filtering."""
    logger.info(f"Creating secure view for {table_name}")
    
    try:
        # Get table configuration
        if table_name not in table_config:
            logger.warning(f"No configuration found for table {table_name}")
            return
        
        config = table_config[table_name]
        schema_def = config.get("schema", [])
        
        # Build masked columns
        masked_columns = []
        
        for col_def in schema_def:
            col_name = col_def["name"]
            
            # Apply masking based on column name
            if col_name == "customer_email_address":
                masked_columns.append(f"mask_email({col_name}, current_user()) AS {col_name}")
            elif col_name == "phone_number":
                masked_columns.append(f"mask_phone({col_name}, current_user()) AS {col_name}")
            elif col_name == "address":
                masked_columns.append(f"mask_address({col_name}, current_user()) AS {col_name}")
            elif col_name == "customer_id":
                masked_columns.append(f"mask_customer_id({col_name}, current_user()) AS {col_name}")
            elif col_name == "loyalty_card_id":
                masked_columns.append(f"mask_loyalty_card_id({col_name}, current_user()) AS {col_name}")
            else:
                masked_columns.append(col_name)
        
        # Build filter conditions
        filter_conditions = []
        
        if table_name == "dim_customer":
            filter_conditions.extend([
                "filter_customer_rows(customer_type, is_active, registration_date, current_user())",
                "filter_by_date_range(registration_date, current_user(), 365)",
                "filter_by_geography(province, current_user())",
                "filter_by_data_quality(data_quality_score, primary_key_valid, current_user())",
                "filter_by_business_rules(customer_type, is_active, loyalty_card_id, current_user())"
            ])
        
        # Combine components
        columns_sql = ",\n    ".join(masked_columns)
        where_clause = " AND ".join(filter_conditions) if filter_conditions else "TRUE"
        
        # Create secure view
        view_name = f"{table_name}_secure"
        
        create_view_sql = f"""
        CREATE OR REPLACE VIEW {view_name} AS
        SELECT 
            {columns_sql}
        FROM {table_name}
        WHERE {where_clause}
        """
        
        logger.info(f"Creating secure view: {view_name}")
        spark.sql(create_view_sql)
        logger.info(f"✅ Successfully created secure view {view_name}")
        
    except Exception as e:
        logger.error(f"Error creating secure view for {table_name}: {str(e)}")
        raise

# COMMAND ----------

# MAGIC %md
# MAGIC ## Main Execution

# COMMAND ----------

# Apply row filtering to configured tables
logger.info("Starting row filtering application")

try:
    # Apply filtering to customer table
    apply_row_filters_to_table("dim_customer", logger)
    
    # Create secure view (masked + filtered)
    create_secure_view("dim_customer", logger)
    
    # Add other tables as needed
    # apply_row_filters_to_table("dim_product", logger)
    # apply_row_filters_to_table("fact_sales", logger)
    
    logger.info("✅ Row filtering application completed successfully")
    
except Exception as e:
    logger.error(f"❌ Failed to apply row filtering: {str(e)}")
    raise

# COMMAND ----------

# MAGIC %md
# MAGIC ## Testing Functions

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Test the filtering functions
# MAGIC SELECT 
# MAGIC   has_full_access('<EMAIL>') AS admin_full_access,
# MAGIC   has_restricted_access('<EMAIL>') AS user_restricted_access,
# MAGIC   filter_customer_rows('Individual', TRUE, CURRENT_DATE(), '<EMAIL>') AS individual_filter,
# MAGIC   filter_by_date_range(DATE_SUB(CURRENT_DATE(), 100), '<EMAIL>', 365) AS recent_date_filter,
# MAGIC   filter_by_geography('ON', '<EMAIL>') AS ontario_filter,
# MAGIC   filter_by_data_quality(0.9, TRUE, '<EMAIL>') AS quality_filter
